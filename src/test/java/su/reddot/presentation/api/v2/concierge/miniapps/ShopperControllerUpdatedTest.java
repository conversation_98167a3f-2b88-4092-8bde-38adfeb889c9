package su.reddot.presentation.api.v2.concierge.miniapps;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import su.reddot.domain.service.concierge.miniapps.ShopperService;
import su.reddot.domain.service.dto.concierge.ShopperBrandFilterDto;
import su.reddot.domain.service.dto.concierge.ShopperBrandsRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoryFilterDto;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ShopperController.class)
class ShopperControllerUpdatedTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ShopperService shopperService;

    @Autowired
    private ObjectMapper objectMapper;

    private ShopperBrandsRequestDto brandsRequest;
    private ShopperCategoriesRequestDto categoriesRequest;
    private ShopperBrandsResponseDto brandsResponse;
    private ShopperCategoriesResponseDto categoriesResponse;

    @BeforeEach
    void setUp() {
        brandsRequest = new ShopperBrandsRequestDto();
        brandsRequest.setMaxResults(50);
        brandsRequest.setOnlyWithProducts(true);
        brandsRequest.setPage(1);
        brandsRequest.setPageSize(20);

        categoriesRequest = new ShopperCategoriesRequestDto();
        categoriesRequest.setMaxResults(50);
        categoriesRequest.setOnlyNonEmpty(true);
        categoriesRequest.setPage(1);
        categoriesRequest.setPageSize(20);

        ShopperBrandFilterDto brand = new ShopperBrandFilterDto()
                .setId(1L)
                .setName("Test Brand")
                .setUrlName("test-brand")
                .setProductsCount(10)
                .setIsHidden(false);

        ShopperCategoryFilterDto category = new ShopperCategoryFilterDto()
                .setId(1L)
                .setDisplayName("Test Category")
                .setFullName("Test Category Full")
                .setProductsCount(20)
                .setHasChildren(false);

        brandsResponse = new ShopperBrandsResponseDto()
                .setBrands(Collections.singletonList(brand))
                .setTotalCount(1)
                .setCurrentPage(1)
                .setPageSize(20)
                .setTotalPages(1)
                .setHasNext(false)
                .setHasPrevious(false);

        categoriesResponse = new ShopperCategoriesResponseDto()
                .setCategories(Collections.singletonList(category))
                .setTotalCount(1)
                .setCurrentPage(1)
                .setPageSize(20)
                .setTotalPages(1)
                .setHasNext(false)
                .setHasPrevious(false);
    }

    @Test
    @WithMockUser
    void testGetBrands_WithValidRequest_ShouldReturnSuccess() throws Exception {
        // Given
        when(shopperService.getBrands(any(ShopperBrandsRequestDto.class))).thenReturn(brandsResponse);

        // When & Then
        mockMvc.perform(post("/api/v2/concierge/miniapps/shopper/brands")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(brandsRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Бренды успешно получены"))
                .andExpect(jsonPath("$.data.brands").isArray())
                .andExpect(jsonPath("$.data.brands[0].id").value(1))
                .andExpect(jsonPath("$.data.brands[0].name").value("Test Brand"))
                .andExpect(jsonPath("$.data.totalCount").value(1))
                .andExpect(jsonPath("$.data.currentPage").value(1))
                .andExpect(jsonPath("$.data.pageSize").value(20))
                .andExpect(jsonPath("$.data.totalPages").value(1))
                .andExpect(jsonPath("$.data.hasNext").value(false))
                .andExpect(jsonPath("$.data.hasPrevious").value(false));
    }

    @Test
    @WithMockUser
    void testGetCategories_WithValidRequest_ShouldReturnSuccess() throws Exception {
        // Given
        when(shopperService.getCategories(any(ShopperCategoriesRequestDto.class))).thenReturn(categoriesResponse);

        // When & Then
        mockMvc.perform(post("/api/v2/concierge/miniapps/shopper/categories")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(categoriesRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Категории успешно получены"))
                .andExpect(jsonPath("$.data.categories").isArray())
                .andExpect(jsonPath("$.data.categories[0].id").value(1))
                .andExpect(jsonPath("$.data.categories[0].displayName").value("Test Category"))
                .andExpect(jsonPath("$.data.totalCount").value(1))
                .andExpect(jsonPath("$.data.currentPage").value(1))
                .andExpect(jsonPath("$.data.pageSize").value(20))
                .andExpect(jsonPath("$.data.totalPages").value(1))
                .andExpect(jsonPath("$.data.hasNext").value(false))
                .andExpect(jsonPath("$.data.hasPrevious").value(false));
    }

    @Test
    @WithMockUser
    void testGetBrands_WithSearch_ShouldReturnFilteredResults() throws Exception {
        // Given
        brandsRequest.setSearch("Test");
        when(shopperService.getBrands(any(ShopperBrandsRequestDto.class))).thenReturn(brandsResponse);

        // When & Then
        mockMvc.perform(post("/api/v2/concierge/miniapps/shopper/brands")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(brandsRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.brands[0].name").value("Test Brand"));
    }

    @Test
    @WithMockUser
    void testGetCategories_WithParentCategoryIds_ShouldReturnChildCategories() throws Exception {
        // Given
        categoriesRequest.setParentCategoryIds(Collections.singletonList(1L));
        when(shopperService.getCategories(any(ShopperCategoriesRequestDto.class))).thenReturn(categoriesResponse);

        // When & Then
        mockMvc.perform(post("/api/v2/concierge/miniapps/shopper/categories")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(categoriesRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.categories[0].id").value(1));
    }

    @Test
    void testGetBrands_WithoutAuthentication_ShouldReturnUnauthorized() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/v2/concierge/miniapps/shopper/brands")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(brandsRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void testGetCategories_WithoutAuthentication_ShouldReturnUnauthorized() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/v2/concierge/miniapps/shopper/categories")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(categoriesRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser
    void testGetBrands_WithServiceException_ShouldReturnError() throws Exception {
        // Given
        when(shopperService.getBrands(any(ShopperBrandsRequestDto.class)))
                .thenThrow(new RuntimeException("Service error"));

        // When & Then
        mockMvc.perform(post("/api/v2/concierge/miniapps/shopper/brands")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(brandsRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Service error"));
    }

    @Test
    @WithMockUser
    void testGetCategories_WithServiceException_ShouldReturnError() throws Exception {
        // Given
        when(shopperService.getCategories(any(ShopperCategoriesRequestDto.class)))
                .thenThrow(new RuntimeException("Service error"));

        // When & Then
        mockMvc.perform(post("/api/v2/concierge/miniapps/shopper/categories")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(categoriesRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Service error"));
    }

    @Test
    @WithMockUser
    void testGetBrands_WithPagination_ShouldReturnPaginatedResults() throws Exception {
        // Given
        brandsRequest.setPage(2);
        brandsRequest.setPageSize(10);
        
        ShopperBrandsResponseDto paginatedResponse = new ShopperBrandsResponseDto()
                .setBrands(Collections.singletonList(brandsResponse.getBrands().get(0)))
                .setTotalCount(25)
                .setCurrentPage(2)
                .setPageSize(10)
                .setTotalPages(3)
                .setHasNext(true)
                .setHasPrevious(true);
        
        when(shopperService.getBrands(any(ShopperBrandsRequestDto.class))).thenReturn(paginatedResponse);

        // When & Then
        mockMvc.perform(post("/api/v2/concierge/miniapps/shopper/brands")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(brandsRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalCount").value(25))
                .andExpect(jsonPath("$.data.currentPage").value(2))
                .andExpect(jsonPath("$.data.pageSize").value(10))
                .andExpect(jsonPath("$.data.totalPages").value(3))
                .andExpect(jsonPath("$.data.hasNext").value(true))
                .andExpect(jsonPath("$.data.hasPrevious").value(true));
    }
}
