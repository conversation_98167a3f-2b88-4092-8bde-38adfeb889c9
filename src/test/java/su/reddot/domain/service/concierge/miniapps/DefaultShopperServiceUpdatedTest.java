package su.reddot.domain.service.concierge.miniapps;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.CategoryDTO;
import su.reddot.domain.service.dto.concierge.ShopperBrandsRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DefaultShopperServiceUpdatedTest {

    @Mock
    private BrandService brandService;

    @Mock
    private CategoryService categoryService;

    @InjectMocks
    private DefaultShopperService shopperService;

    private ShopperBrandsRequestDto brandsRequest;
    private ShopperCategoriesRequestDto categoriesRequest;
    private BrandDTO testBrand;
    private CategoryDTO testCategory;

    @BeforeEach
    void setUp() {
        brandsRequest = new ShopperBrandsRequestDto();
        brandsRequest.setMaxResults(50);
        brandsRequest.setOnlyWithProducts(true);
        brandsRequest.setPage(1);
        brandsRequest.setPageSize(20);

        categoriesRequest = new ShopperCategoriesRequestDto();
        categoriesRequest.setMaxResults(50);
        categoriesRequest.setOnlyNonEmpty(true);
        categoriesRequest.setIncludeChildren(true);
        categoriesRequest.setMaxDepth(1);
        categoriesRequest.setPage(1);
        categoriesRequest.setPageSize(20);

        testBrand = new BrandDTO()
                .setId(1L)
                .setName("Test Brand")
                .setUrlName("test-brand")
                .setTransliterateName("test-brand")
                .setProductsCount(10)
                .setIsHidden(false)
                .setTitle("Test Brand Title")
                .setHiddenDescription("Test Description");

        testCategory = new CategoryDTO()
                .setId(1L)
                .setDisplayName("Test Category")
                .setSingularName("Test Category")
                .setFullName("Test Category Full")
                .setPluralName("Test Categories")
                .setUrl("test-category")
                .setAlternativeUrl("test-category-alt")
                .setIcon("test-icon")
                .setProductsCount(20)
                .setHasChildren(false)
                .setDefaultSizeType(SizeType.EU);
    }

    @Test
    void testGetBrands_WithDefaultRequest_ShouldReturnBrandsWithPagination() {
        // Given
        List<BrandDTO> brands = Collections.singletonList(testBrand);
        when(brandService.getBrandsAvailableForPublishing(null)).thenReturn(brands);

        // When
        ShopperBrandsResponseDto response = shopperService.getBrands(brandsRequest);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getBrands().size());
        assertEquals(1, response.getTotalCount());
        assertEquals(1, response.getCurrentPage());
        assertEquals(20, response.getPageSize());
        assertEquals(1, response.getTotalPages());
        assertFalse(response.getHasNext());
        assertFalse(response.getHasPrevious());

        assertEquals(testBrand.getId(), response.getBrands().get(0).getId());
        assertEquals(testBrand.getName(), response.getBrands().get(0).getName());
    }

    @Test
    void testGetBrands_WithSearch_ShouldReturnFilteredBrands() {
        // Given
        brandsRequest.setSearch("Test");
        List<BrandDTO> brands = Collections.singletonList(testBrand);
        when(brandService.getBrandByNameContains("Test")).thenReturn(brands);

        // When
        ShopperBrandsResponseDto response = shopperService.getBrands(brandsRequest);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getBrands().size());
        verify(brandService).getBrandByNameContains("Test");
    }

    @Test
    void testGetBrands_WithCategoryIds_ShouldReturnBrandsForCategory() {
        // Given
        brandsRequest.setCategoryIds(Collections.singletonList(1L));
        Page<BrandDTO> brandPage = new PageImpl<>(Collections.singletonList(testBrand));
        when(brandService.getAllBrandsWithAvailableProductsFiltered(eq(""), isNull(), any(PageRequest.class), eq(1L)))
                .thenReturn(brandPage);

        // When
        ShopperBrandsResponseDto response = shopperService.getBrands(brandsRequest);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getBrands().size());
        verify(brandService).getAllBrandsWithAvailableProductsFiltered(eq(""), isNull(), any(PageRequest.class), eq(1L));
    }

    @Test
    void testGetBrands_WithPagination_ShouldReturnCorrectPage() {
        // Given
        brandsRequest.setPage(2);
        brandsRequest.setPageSize(1);
        List<BrandDTO> brands = Arrays.asList(testBrand, testBrand);
        when(brandService.getBrandsAvailableForPublishing(null)).thenReturn(brands);

        // When
        ShopperBrandsResponseDto response = shopperService.getBrands(brandsRequest);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getBrands().size());
        assertEquals(2, response.getTotalCount());
        assertEquals(2, response.getCurrentPage());
        assertEquals(2, response.getTotalPages());
        assertFalse(response.getHasNext());
        assertTrue(response.getHasPrevious());
    }

    @Test
    void testGetCategories_WithDefaultRequest_ShouldReturnCategoriesWithPagination() {
        // Given
        List<CategoryDTO> categories = Collections.singletonList(testCategory);
        when(categoryService.getDirectChildrenCategoryDTOsCached(CategoryService.ROOT_CATEGORY_ID, true))
                .thenReturn(categories);

        // When
        ShopperCategoriesResponseDto response = shopperService.getCategories(categoriesRequest);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getCategories().size());
        assertEquals(1, response.getTotalCount());
        assertEquals(1, response.getCurrentPage());
        assertEquals(20, response.getPageSize());
        assertEquals(1, response.getTotalPages());
        assertFalse(response.getHasNext());
        assertFalse(response.getHasPrevious());

        assertEquals(testCategory.getId(), response.getCategories().get(0).getId());
        assertEquals(testCategory.getDisplayName(), response.getCategories().get(0).getDisplayName());
    }

    @Test
    void testGetCategories_WithParentCategoryIds_ShouldReturnChildCategories() {
        // Given
        categoriesRequest.setParentCategoryIds(Collections.singletonList(1L));
        List<CategoryDTO> categories = Collections.singletonList(testCategory);
        when(categoryService.getDirectChildrenCategoryDTOsCached(1L, true)).thenReturn(categories);

        // When
        ShopperCategoriesResponseDto response = shopperService.getCategories(categoriesRequest);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getCategories().size());
        verify(categoryService).getDirectChildrenCategoryDTOsCached(1L, true);
    }

    @Test
    void testGetCategories_WithSearch_ShouldReturnFilteredCategories() {
        // Given
        categoriesRequest.setSearch("Test");
        List<CategoryDTO> rootCategories = Collections.singletonList(testCategory);
        when(categoryService.getDirectChildrenCategoryDTOsCached(CategoryService.ROOT_CATEGORY_ID, true))
                .thenReturn(rootCategories);

        // When
        ShopperCategoriesResponseDto response = shopperService.getCategories(categoriesRequest);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getCategories().size());
        assertEquals("Test Category", response.getCategories().get(0).getDisplayName());
    }

    @Test
    void testGetBrands_WithException_ShouldReturnEmptyResponse() {
        // Given
        when(brandService.getBrandsAvailableForPublishing(null)).thenThrow(new RuntimeException("Service error"));

        // When
        ShopperBrandsResponseDto response = shopperService.getBrands(brandsRequest);

        // Then
        assertNotNull(response);
        assertTrue(response.getBrands().isEmpty());
        assertEquals(0, response.getTotalCount());
        assertEquals(1, response.getCurrentPage());
        assertEquals(20, response.getPageSize());
        assertEquals(0, response.getTotalPages());
        assertFalse(response.getHasNext());
        assertFalse(response.getHasPrevious());
    }

    @Test
    void testGetCategories_WithException_ShouldReturnEmptyResponse() {
        // Given
        when(categoryService.getDirectChildrenCategoryDTOsCached(CategoryService.ROOT_CATEGORY_ID, true))
                .thenThrow(new RuntimeException("Service error"));

        // When
        ShopperCategoriesResponseDto response = shopperService.getCategories(categoriesRequest);

        // Then
        assertNotNull(response);
        assertTrue(response.getCategories().isEmpty());
        assertEquals(0, response.getTotalCount());
        assertEquals(1, response.getCurrentPage());
        assertEquals(20, response.getPageSize());
        assertEquals(0, response.getTotalPages());
        assertFalse(response.getHasNext());
        assertFalse(response.getHasPrevious());
    }
}
