package su.reddot.presentation.api.v2.concierge.miniapps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;
import su.reddot.presentation.api.v2.Api2Response;

@RequestMapping("/api/v2/concierge/miniapps/shopper")
public interface ShopperControllerDelegate {

    @PostMapping("/brands")
    @Operation(
            summary = "Получение брендов для шопера",
            description = "Получение списка доступных брендов для фильтрации товаров в консьерж-сервисе",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Бренды успешно получены",
                            content = @Content(schema = @Schema(implementation = ShopperBrandsResponse.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Пользователь не авторизован"
                    ),
                    @ApiResponse(
                            responseCode = "403",
                            description = "Доступ запрещен"
                    )
            }
    )
    Api2Response<ShopperBrandsResponseDto> getBrands();

    @PostMapping("/categories")
    @Operation(
            summary = "Получение категорий для шопера",
            description = "Получение списка доступных категорий для фильтрации товаров в консьерж-сервисе",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Категории успешно получены",
                            content = @Content(schema = @Schema(implementation = ShopperCategoriesResponse.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Пользователь не авторизован"
                    ),
                    @ApiResponse(
                            responseCode = "403",
                            description = "Доступ запрещен"
                    )
            }
    )
    Api2Response<ShopperCategoriesResponseDto> getCategories();

    /**
     * Вспомогательный класс для документации ответа с брендами
     */
    @Schema(description = "Ответ с брендами для шопера")
    class ShopperBrandsResponse extends Api2Response<ShopperBrandsResponseDto> {
        @Schema(description = "Данные брендов")
        private ShopperBrandsResponseDto data;
    }

    /**
     * Вспомогательный класс для документации ответа с категориями
     */
    @Schema(description = "Ответ с категориями для шопера")
    class ShopperCategoriesResponse extends Api2Response<ShopperCategoriesResponseDto> {
        @Schema(description = "Данные категорий")
        private ShopperCategoriesResponseDto data;
    }
}
