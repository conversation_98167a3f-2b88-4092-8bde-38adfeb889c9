package su.reddot.presentation.api.v2.concierge.miniapps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import su.reddot.domain.service.dto.concierge.ShopperFilterRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperFilterResponseDto;
import su.reddot.presentation.api.v2.Api2Response;

@RequestMapping("/api/v2/concierge/miniapps/shopper")
public interface ShopperControllerDelegate {

    @PostMapping("/filters")
    @Operation(
            summary = "Получение фильтров для шопера",
            description = "Получение списка доступных брендов и категорий для фильтрации товаров в консьерж-сервисе",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Фильтры успешно получены",
                            content = @Content(schema = @Schema(implementation = ShopperFilterResponse.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Некорректные параметры запроса"
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Пользователь не авторизован"
                    ),
                    @ApiResponse(
                            responseCode = "403",
                            description = "Доступ запрещен"
                    )
            }
    )
    Api2Response<ShopperFilterResponseDto> getFilters(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Параметры фильтрации",
                    required = true,
                    content = @Content(schema = @Schema(implementation = ShopperFilterRequestDto.class))
            )
            @RequestBody ShopperFilterRequestDto request);

    /**
     * Вспомогательный класс для документации ответа с фильтрами
     */
    @Schema(description = "Ответ с фильтрами для шопера")
    class ShopperFilterResponse extends Api2Response<ShopperFilterResponseDto> {
        @Schema(description = "Данные фильтров")
        private ShopperFilterResponseDto data;
    }
}
