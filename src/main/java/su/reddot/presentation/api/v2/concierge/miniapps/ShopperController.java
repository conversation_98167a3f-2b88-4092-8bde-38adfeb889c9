package su.reddot.presentation.api.v2.concierge.miniapps;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.concierge.miniapps.ShopperService;
import su.reddot.domain.service.dto.concierge.ShopperBrandsRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;
import su.reddot.presentation.api.v2.Api2Response;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class ShopperController implements ShopperControllerDelegate {

    private final ShopperService shopperService;

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<ShopperBrandsResponseDto> getBrands(ShopperBrandsRequestDto request) {
        log.debug("Getting brands for shopper with request: {}", request);

        Api2Response<ShopperBrandsResponseDto> response = Api2Response.create();

        try {
            ShopperBrandsResponseDto brands = shopperService.getBrands(request);
            return response.success("Бренды успешно получены", brands);
        } catch (Exception e) {
            log.error("Error getting brands for shopper", e);
            return Api2Response.error(e, "Ошибка получения брендов", "Не удалось получить бренды");
        }
    }

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<ShopperCategoriesResponseDto> getCategories(ShopperCategoriesRequestDto request) {
        log.debug("Getting categories for shopper with request: {}", request);

        Api2Response<ShopperCategoriesResponseDto> response = Api2Response.create();

        try {
            ShopperCategoriesResponseDto categories = shopperService.getCategories(request);
            return response.success("Категории успешно получены", categories);
        } catch (Exception e) {
            log.error("Error getting categories for shopper", e);
            return Api2Response.error(e, "Ошибка получения категорий", "Не удалось получить категории");
        }
    }
}
