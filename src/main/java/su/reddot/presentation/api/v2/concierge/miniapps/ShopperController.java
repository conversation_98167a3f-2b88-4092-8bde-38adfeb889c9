package su.reddot.presentation.api.v2.concierge.miniapps;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.concierge.miniapps.ShopperService;
import su.reddot.domain.service.dto.concierge.ShopperFilterRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperFilterResponseDto;
import su.reddot.presentation.api.v2.Api2Response;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class ShopperController implements ShopperControllerDelegate {

    private final ShopperService shopperService;

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<ShopperFilterResponseDto> getFilters(ShopperFilterRequestDto request) {
        log.debug("Получение фильтров для шопера: {}", request);

        Api2Response<ShopperFilterResponseDto> response = Api2Response.create();

        ShopperFilterResponseDto filters = shopperService.getFilters(request);
        return response.success("Фильтры успешно получены", filters);

    }
}
