package su.reddot.presentation.api.v2.concierge.miniapps;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.concierge.miniapps.ShopperService;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;
import su.reddot.presentation.api.v2.Api2Response;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class ShopperController implements ShopperControllerDelegate {

    private final ShopperService shopperService;

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<ShopperBrandsResponseDto> getBrands() {
        Api2Response<ShopperBrandsResponseDto> response = Api2Response.create();
        ShopperBrandsResponseDto brands = shopperService.getBrands();
        return response.success("Бренды успешно получены", brands);
    }

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<ShopperCategoriesResponseDto> getCategories() {
        Api2Response<ShopperCategoriesResponseDto> response = Api2Response.create();

        ShopperCategoriesResponseDto categories = shopperService.getCategories();
        return response.success("Категории успешно получены", categories);
    }
}
