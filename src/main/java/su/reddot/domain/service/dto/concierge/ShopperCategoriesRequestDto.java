package su.reddot.domain.service.dto.concierge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "Запрос на получение категорий для шопера")
public class ShopperCategoriesRequestDto {
    
    @Schema(description = "Список идентификаторов родительских категорий", example = "[1, 2, 3]")
    private List<Long> parentCategoryIds;
    
    @Schema(description = "Поисковый запрос для фильтрации категорий", example = "платья")
    private String search;
    
    @Schema(description = "Максимальное количество категорий в ответе", example = "50", defaultValue = "50")
    private Integer maxResults = 50;
    
    @Schema(description = "Включать ли только категории с доступными товарами", example = "true", defaultValue = "true")
    private Boolean onlyNonEmpty = true;
    
    @Schema(description = "Включать ли дочерние категории", example = "true", defaultValue = "true")
    private Boolean includeChildren = true;
    
    @Schema(description = "Максимальная глубина вложенности дочерних категорий", example = "2", defaultValue = "1")
    private Integer maxDepth = 1;
    
    @Schema(description = "Номер страницы для пагинации", example = "1", defaultValue = "1")
    private Integer page = 1;
    
    @Schema(description = "Размер страницы для пагинации", example = "20", defaultValue = "20")
    private Integer pageSize = 20;
}
