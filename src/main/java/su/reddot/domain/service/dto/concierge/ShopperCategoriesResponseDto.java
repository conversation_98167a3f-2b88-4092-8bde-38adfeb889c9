package su.reddot.domain.service.dto.concierge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "Ответ с категориями для шопера")
public class ShopperCategoriesResponseDto {
    
    @Schema(description = "Список доступных категорий")
    private List<ShopperCategoryFilterDto> categories;
    
    @Schema(description = "Общее количество найденных категорий", example = "45")
    private Integer totalCount;
    
    @Schema(description = "Текущая страница", example = "1")
    private Integer currentPage;
    
    @Schema(description = "Размер страницы", example = "20")
    private Integer pageSize;
    
    @Schema(description = "Общее количество страниц", example = "3")
    private Integer totalPages;
    
    @Schema(description = "Есть ли следующая страница", example = "false")
    private Boolean hasNext;
    
    @Schema(description = "Есть ли предыдущая страница", example = "false")
    private Boolean hasPrevious;
}
