package su.reddot.domain.service.dto.concierge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "Ответ с фильтрами для шопера")
public class ShopperFilterResponseDto {
    
    @Schema(description = "Список доступных брендов")
    private List<ShopperBrandFilterDto> brands;
    
    @Schema(description = "Список доступных категорий")
    private List<ShopperCategoryFilterDto> categories;
    
    @Schema(description = "Общее количество найденных брендов", example = "150")
    private Integer totalBrandsCount;
    
    @Schema(description = "Общее количество найденных категорий", example = "45")
    private Integer totalCategoriesCount;
    
    @Schema(description = "Были ли применены ограничения по количеству результатов", example = "false")
    private Boolean hasMoreResults;
}
