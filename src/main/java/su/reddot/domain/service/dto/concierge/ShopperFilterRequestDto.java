package su.reddot.domain.service.dto.concierge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "Запрос на получение фильтров для шопера")
public class ShopperFilterRequestDto {
    
    @Schema(description = "Список идентификаторов категорий для фильтрации", example = "[1, 2, 3]")
    private List<Long> categoryIds;
    
    @Schema(description = "Список идентификаторов брендов для фильтрации", example = "[10, 20, 30]")
    private List<Long> brandIds;
    
    @Schema(description = "Поисковый запрос для фильтрации брендов", example = "Gucci")
    private String brandSearch;
    
    @Schema(description = "Поисковый запрос для фильтрации категорий", example = "платья")
    private String categorySearch;
    
    @Schema(description = "Максимальное количество брендов в ответе", example = "50", defaultValue = "50")
    private Integer maxBrands = 50;
    
    @Schema(description = "Максимальное количество категорий в ответе", example = "50", defaultValue = "50")
    private Integer maxCategories = 50;
    
    @Schema(description = "Включать ли только бренды с доступными товарами", example = "true", defaultValue = "true")
    private Boolean onlyWithProducts = true;
    
    @Schema(description = "Включать ли только категории с доступными товарами", example = "true", defaultValue = "true")
    private Boolean onlyNonEmptyCategories = true;
}
