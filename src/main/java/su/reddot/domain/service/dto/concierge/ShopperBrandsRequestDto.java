package su.reddot.domain.service.dto.concierge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "Запрос на получение брендов для шопера")
public class ShopperBrandsRequestDto {
    
    @Schema(description = "Список идентификаторов категорий для фильтрации брендов", example = "[1, 2, 3]")
    private List<Long> categoryIds;
    
    @Schema(description = "Поисковый запрос для фильтрации брендов", example = "Gucci")
    private String search;
    
    @Schema(description = "Максимальное количество брендов в ответе", example = "50", defaultValue = "50")
    private Integer maxResults = 50;
    
    @Schema(description = "Включать ли только бренды с доступными товарами", example = "true", defaultValue = "true")
    private Boolean onlyWithProducts = true;
    
    @Schema(description = "Номер страницы для пагинации", example = "1", defaultValue = "1")
    private Integer page = 1;
    
    @Schema(description = "Размер страницы для пагинации", example = "20", defaultValue = "20")
    private Integer pageSize = 20;
}
