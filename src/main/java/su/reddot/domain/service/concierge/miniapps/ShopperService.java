package su.reddot.domain.service.concierge.miniapps;

import su.reddot.domain.service.dto.concierge.ShopperBrandsRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;

/**
 * Сервис для работы с фильтрами шопера в консьерж-сервисе
 */
public interface ShopperService {

    /**
     * Получение брендов для шопера
     *
     * @param request параметры запроса брендов
     * @return ответ с брендами
     */
    ShopperBrandsResponseDto getBrands(ShopperBrandsRequestDto request);

    /**
     * Получение категорий для шопера
     *
     * @param request параметры запроса категорий
     * @return ответ с категориями
     */
    ShopperCategoriesResponseDto getCategories(ShopperCategoriesRequestDto request);
}
