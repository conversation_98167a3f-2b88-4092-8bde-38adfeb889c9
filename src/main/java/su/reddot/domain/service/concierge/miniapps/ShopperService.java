package su.reddot.domain.service.concierge.miniapps;

import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;

/**
 * Сервис для работы с фильтрами шопера в консьерж-сервисе
 */
public interface ShopperService {

    /**
     * Получение брендов для шопера
     *
     * @return ответ с брендами
     */
    ShopperBrandsResponseDto getBrands();

    /**
     * Получение категорий для шопера
     *
     * @return ответ с категориями
     */
    ShopperCategoriesResponseDto getCategories();
}
