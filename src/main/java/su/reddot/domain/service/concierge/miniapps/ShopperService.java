package su.reddot.domain.service.concierge.miniapps;

import su.reddot.domain.service.dto.concierge.ShopperFilterRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperFilterResponseDto;

/**
 * Сервис для работы с фильтрами шопера в консьерж-сервисе
 */
public interface ShopperService {
    
    /**
     * Получение фильтров брендов и категорий для шопера
     * 
     * @param request параметры запроса фильтров
     * @return ответ с фильтрами брендов и категорий
     */
    ShopperFilterResponseDto getFilters(ShopperFilterRequestDto request);
}
