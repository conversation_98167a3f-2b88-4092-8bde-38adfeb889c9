package su.reddot.domain.service.concierge.miniapps;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.CategoryDTO;
import su.reddot.domain.service.dto.concierge.ShopperBrandFilterDto;
import su.reddot.domain.service.dto.concierge.ShopperBrandsRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoryFilterDto;
import su.reddot.domain.service.dto.concierge.ShopperFilterRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperFilterResponseDto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultShopperService implements ShopperService {
    
    private final BrandService brandService;
    private final CategoryService categoryService;
    
    @Override
    public ShopperBrandsResponseDto getBrands(ShopperBrandsRequestDto request) {
        log.debug("Getting brands with request: {}", request);

        try {
            List<BrandDTO> brandDTOs = getBrandDTOs(request);

            // Пагинация
            int totalCount = brandDTOs.size();
            int startIndex = (request.getPage() - 1) * request.getPageSize();
            int endIndex = Math.min(startIndex + request.getPageSize(), totalCount);

            List<BrandDTO> pagedBrands = brandDTOs.subList(startIndex, endIndex);
            List<ShopperBrandFilterDto> brands = pagedBrands.stream()
                    .map(this::mapBrandToFilterDto)
                    .collect(Collectors.toList());

            int totalPages = (int) Math.ceil((double) totalCount / request.getPageSize());

            return new ShopperBrandsResponseDto()
                    .setBrands(brands)
                    .setTotalCount(totalCount)
                    .setCurrentPage(request.getPage())
                    .setPageSize(request.getPageSize())
                    .setTotalPages(totalPages)
                    .setHasNext(request.getPage() < totalPages)
                    .setHasPrevious(request.getPage() > 1);

        } catch (Exception e) {
            log.error("Error getting brands", e);
            return new ShopperBrandsResponseDto()
                    .setBrands(Collections.emptyList())
                    .setTotalCount(0)
                    .setCurrentPage(request.getPage())
                    .setPageSize(request.getPageSize())
                    .setTotalPages(0)
                    .setHasNext(false)
                    .setHasPrevious(false);
        }
    }

    @Override
    public ShopperCategoriesResponseDto getCategories(ShopperCategoriesRequestDto request) {
        log.debug("Getting categories with request: {}", request);

        try {
            List<CategoryDTO> categoryDTOs = getCategoryDTOs(request);

            // Пагинация
            int totalCount = categoryDTOs.size();
            int startIndex = (request.getPage() - 1) * request.getPageSize();
            int endIndex = Math.min(startIndex + request.getPageSize(), totalCount);

            List<CategoryDTO> pagedCategories = categoryDTOs.subList(startIndex, endIndex);
            List<ShopperCategoryFilterDto> categories = pagedCategories.stream()
                    .map(this::mapCategoryToFilterDto)
                    .collect(Collectors.toList());

            int totalPages = (int) Math.ceil((double) totalCount / request.getPageSize());

            return new ShopperCategoriesResponseDto()
                    .setCategories(categories)
                    .setTotalCount(totalCount)
                    .setCurrentPage(request.getPage())
                    .setPageSize(request.getPageSize())
                    .setTotalPages(totalPages)
                    .setHasNext(request.getPage() < totalPages)
                    .setHasPrevious(request.getPage() > 1);

        } catch (Exception e) {
            log.error("Error getting categories", e);
            return new ShopperCategoriesResponseDto()
                    .setCategories(Collections.emptyList())
                    .setTotalCount(0)
                    .setCurrentPage(request.getPage())
                    .setPageSize(request.getPageSize())
                    .setTotalPages(0)
                    .setHasNext(false)
                    .setHasPrevious(false);
        }
    }
    
    private List<BrandDTO> getBrandDTOs(ShopperBrandsRequestDto request) {
        try {
            List<BrandDTO> brandDTOs;
            
            if (StringUtils.hasText(request.getSearch())) {
                // Поиск брендов по названию
                brandDTOs = brandService.getBrandByNameContains(request.getSearch());
            } else if (!CollectionUtils.isEmpty(request.getCategoryIds())) {
                // Получение брендов для конкретных категорий
                Long categoryId = request.getCategoryIds().get(0); // Берем первую категорию
                Page<BrandDTO> brandPage = brandService.getAllBrandsWithAvailableProductsFiltered(
                    "", null, PageRequest.of(0, request.getMaxResults()), categoryId);
                brandDTOs = brandPage.getContent();
            } else {
                // Получение всех доступных брендов
                if (Boolean.TRUE.equals(request.getOnlyWithProducts())) {
                    brandDTOs = brandService.getBrandsAvailableForPublishing(null);
                } else {
                    brandDTOs = brandService.getAllBrandsLiteCached();
                }
            }

            // Ограничение количества результатов
            if (brandDTOs.size() > request.getMaxResults()) {
                brandDTOs = brandDTOs.subList(0, request.getMaxResults());
            }

            return brandDTOs;
                    
        } catch (Exception e) {
            log.error("Error getting brands", e);
            return Collections.emptyList();
        }
    }
    
    private List<CategoryDTO> getCategoryDTOs(ShopperCategoriesRequestDto request) {
        try {
            List<CategoryDTO> categoryDTOs = new ArrayList<>();

            if (StringUtils.hasText(request.getSearch())) {
                // Поиск категорий по названию - получаем корневые категории и фильтруем
                List<CategoryDTO> rootCategories = getRootCategories(Boolean.TRUE.equals(request.getOnlyNonEmpty()));
                categoryDTOs = searchCategoriesRecursively(rootCategories, request.getSearch().toLowerCase());
            } else if (!CollectionUtils.isEmpty(request.getParentCategoryIds())) {
                // Получение дочерних категорий для указанных родительских категорий
                for (Long parentId : request.getParentCategoryIds()) {
                    List<CategoryDTO> children = categoryService.getDirectChildrenCategoryDTOsCached(parentId, Boolean.TRUE.equals(request.getOnlyNonEmpty()));
                    categoryDTOs.addAll(children);
                }
            } else {
                // Получение корневых категорий
                categoryDTOs = getRootCategories(Boolean.TRUE.equals(request.getOnlyNonEmpty()));
            }

            // Ограничение количества результатов
            if (categoryDTOs.size() > request.getMaxResults()) {
                categoryDTOs = categoryDTOs.subList(0, request.getMaxResults());
            }

            return categoryDTOs;

        } catch (Exception e) {
            log.error("Error getting categories", e);
            return Collections.emptyList();
        }
    }
    
    private ShopperBrandFilterDto mapBrandToFilterDto(BrandDTO brandDTO) {
        return new ShopperBrandFilterDto()
                .setId(brandDTO.getId())
                .setName(brandDTO.getName())
                .setUrlName(brandDTO.getUrlName())
                .setTransliterateName(brandDTO.getTransliterateName())
                .setProductsCount(brandDTO.getProductsCount())
                .setIsHidden(brandDTO.getIsHidden())
                .setTitle(brandDTO.getTitle())
                .setHiddenDescription(brandDTO.getHiddenDescription());
    }
    
    private ShopperCategoryFilterDto mapCategoryToFilterDto(CategoryDTO categoryDTO) {
        ShopperCategoryFilterDto dto = new ShopperCategoryFilterDto()
                .setId(categoryDTO.getId())
                .setDisplayName(categoryDTO.getDisplayName())
                .setSingularName(categoryDTO.getSingularName())
                .setFullName(categoryDTO.getFullName())
                .setPluralName(categoryDTO.getPluralName())
                .setUrl(categoryDTO.getUrl())
                .setAlternativeUrl(categoryDTO.getAlternativeUrl())
                .setIcon(categoryDTO.getIcon())
                .setProductsCount(categoryDTO.getProductsCount())
                .setHasChildren(categoryDTO.getHasChildren())
                .setDefaultSizeType(categoryDTO.getDefaultSizeType() != null ? categoryDTO.getDefaultSizeType().name() : null);
        
        // Маппинг дочерних категорий
        if (!CollectionUtils.isEmpty(categoryDTO.getChildren())) {
            List<ShopperCategoryFilterDto> children = categoryDTO.getChildren().stream()
                    .map(this::mapCategoryToFilterDto)
                    .collect(Collectors.toList());
            dto.setChildren(children);
        }
        
        return dto;
    }

    private List<CategoryDTO> getRootCategories(boolean onlyNonEmpty) {
        // Получаем корневые категории (дочерние категории ROOT_CATEGORY_ID)
        return categoryService.getDirectChildrenCategoryDTOsCached(CategoryService.ROOT_CATEGORY_ID, onlyNonEmpty);
    }

    private CategoryDTO getCategoryDTOById(Long categoryId) {
        // Получаем категорию по ID и преобразуем в DTO
        Category category = categoryService.getCategory(categoryId);
        if (category != null) {
            return categoryService.getCategoryDTO(category);
        }
        return null;
    }

    private List<CategoryDTO> searchCategoriesRecursively(List<CategoryDTO> categories, String searchTerm) {
        List<CategoryDTO> result = new ArrayList<>();

        for (CategoryDTO category : categories) {
            if (category.getDisplayName() != null &&
                category.getDisplayName().toLowerCase().contains(searchTerm)) {
                result.add(category);
            }

            // Рекурсивный поиск в дочерних категориях
            if (!CollectionUtils.isEmpty(category.getChildren())) {
                result.addAll(searchCategoriesRecursively(category.getChildren(), searchTerm));
            }
        }

        return result;
    }
}
