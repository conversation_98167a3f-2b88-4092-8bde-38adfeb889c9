package su.reddot.domain.service.concierge.miniapps;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.CategoryDTO;
import su.reddot.domain.service.dto.concierge.ShopperBrandFilterDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoryFilterDto;
import su.reddot.domain.service.dto.concierge.ShopperFilterRequestDto;
import su.reddot.domain.service.dto.concierge.ShopperFilterResponseDto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultShopperService implements ShopperService {
    
    private final BrandService brandService;
    private final CategoryService categoryService;
    
    @Override
    public ShopperFilterResponseDto getFilters(ShopperFilterRequestDto request) {
        ShopperFilterResponseDto response = new ShopperFilterResponseDto();
        
        // Получение брендов
        List<ShopperBrandFilterDto> brands = getBrands(request);
        response.setBrands(brands);
        response.setTotalBrandsCount(brands.size());
        
        // Получение категорий
        List<ShopperCategoryFilterDto> categories = getCategories(request);
        response.setCategories(categories);
        response.setTotalCategoriesCount(categories.size());
        
        // Проверка на ограничения
        boolean hasMoreResults = (brands.size() >= request.getMaxBrands()) || 
                                (categories.size() >= request.getMaxCategories());
        response.setHasMoreResults(hasMoreResults);
        
        return response;
    }
    
    private List<ShopperBrandFilterDto> getBrands(ShopperFilterRequestDto request) {
        try {
            List<BrandDTO> brandDTOs;
            
            if (StringUtils.hasText(request.getBrandSearch())) {
                // Поиск брендов по названию
                brandDTOs = brandService.getBrandByNameContains(request.getBrandSearch());
            } else if (!CollectionUtils.isEmpty(request.getCategoryIds())) {
                // Получение брендов для конкретных категорий
                Long categoryId = request.getCategoryIds().get(0); // Берем первую категорию
                Page<BrandDTO> brandPage = brandService.getAllBrandsWithAvailableProductsFiltered(
                    "", null, PageRequest.of(0, request.getMaxBrands()), categoryId);
                brandDTOs = brandPage.getContent();
            } else {
                // Получение всех доступных брендов
                if (Boolean.TRUE.equals(request.getOnlyWithProducts())) {
                    brandDTOs = brandService.getBrandsAvailableForPublishing(null);
                } else {
                    brandDTOs = brandService.getAllBrandsLiteCached();
                }
            }
            
            // Ограничение количества результатов
            if (brandDTOs.size() > request.getMaxBrands()) {
                brandDTOs = brandDTOs.subList(0, request.getMaxBrands());
            }
            
            return brandDTOs.stream()
                    .map(this::mapBrandToFilterDto)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("Error getting brands", e);
            return Collections.emptyList();
        }
    }
    
    private List<ShopperCategoryFilterDto> getCategories(ShopperFilterRequestDto request) {
        try {
            List<CategoryDTO> categoryDTOs = new ArrayList<>();

            if (StringUtils.hasText(request.getCategorySearch())) {
                // Поиск категорий по названию - получаем корневые категории и фильтруем
                List<CategoryDTO> rootCategories = getRootCategories(Boolean.TRUE.equals(request.getOnlyNonEmptyCategories()));
                categoryDTOs = searchCategoriesRecursively(rootCategories, request.getCategorySearch().toLowerCase());
            } else if (!CollectionUtils.isEmpty(request.getCategoryIds())) {
                // Получение конкретных категорий по ID
                for (Long categoryId : request.getCategoryIds()) {
                    CategoryDTO categoryDTO = getCategoryDTOById(categoryId);
                    if (categoryDTO != null) {
                        categoryDTOs.add(categoryDTO);
                    }
                }
            } else {
                // Получение корневых категорий
                categoryDTOs = getRootCategories(Boolean.TRUE.equals(request.getOnlyNonEmptyCategories()));
            }

            // Ограничение количества результатов
            if (categoryDTOs.size() > request.getMaxCategories()) {
                categoryDTOs = categoryDTOs.subList(0, request.getMaxCategories());
            }

            return categoryDTOs.stream()
                    .map(this::mapCategoryToFilterDto)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error getting categories", e);
            return Collections.emptyList();
        }
    }
    
    private ShopperBrandFilterDto mapBrandToFilterDto(BrandDTO brandDTO) {
        return new ShopperBrandFilterDto()
                .setId(brandDTO.getId())
                .setName(brandDTO.getName())
                .setUrlName(brandDTO.getUrlName())
                .setTransliterateName(brandDTO.getTransliterateName())
                .setProductsCount(brandDTO.getProductsCount())
                .setIsHidden(brandDTO.getIsHidden())
                .setTitle(brandDTO.getTitle())
                .setHiddenDescription(brandDTO.getHiddenDescription());
    }
    
    private ShopperCategoryFilterDto mapCategoryToFilterDto(CategoryDTO categoryDTO) {
        ShopperCategoryFilterDto dto = new ShopperCategoryFilterDto()
                .setId(categoryDTO.getId())
                .setDisplayName(categoryDTO.getDisplayName())
                .setSingularName(categoryDTO.getSingularName())
                .setFullName(categoryDTO.getFullName())
                .setPluralName(categoryDTO.getPluralName())
                .setUrl(categoryDTO.getUrl())
                .setAlternativeUrl(categoryDTO.getAlternativeUrl())
                .setIcon(categoryDTO.getIcon())
                .setProductsCount(categoryDTO.getProductsCount())
                .setHasChildren(categoryDTO.getHasChildren())
                .setDefaultSizeType(categoryDTO.getDefaultSizeType() != null ? categoryDTO.getDefaultSizeType().name() : null);
        
        // Маппинг дочерних категорий
        if (!CollectionUtils.isEmpty(categoryDTO.getChildren())) {
            List<ShopperCategoryFilterDto> children = categoryDTO.getChildren().stream()
                    .map(this::mapCategoryToFilterDto)
                    .collect(Collectors.toList());
            dto.setChildren(children);
        }
        
        return dto;
    }

    private List<CategoryDTO> getRootCategories(boolean onlyNonEmpty) {
        // Получаем корневые категории (дочерние категории ROOT_CATEGORY_ID)
        return categoryService.getDirectChildrenCategoryDTOsCached(CategoryService.ROOT_CATEGORY_ID, onlyNonEmpty);
    }

    private CategoryDTO getCategoryDTOById(Long categoryId) {
        // Получаем категорию по ID и преобразуем в DTO
        Category category = categoryService.getCategory(categoryId);
        if (category != null) {
            return categoryService.getCategoryDTO(category);
        }
        return null;
    }

    private List<CategoryDTO> searchCategoriesRecursively(List<CategoryDTO> categories, String searchTerm) {
        List<CategoryDTO> result = new ArrayList<>();

        for (CategoryDTO category : categories) {
            if (category.getDisplayName() != null &&
                category.getDisplayName().toLowerCase().contains(searchTerm)) {
                result.add(category);
            }

            // Рекурсивный поиск в дочерних категориях
            if (!CollectionUtils.isEmpty(category.getChildren())) {
                result.addAll(searchCategoriesRecursively(category.getChildren(), searchTerm));
            }
        }

        return result;
    }
}
