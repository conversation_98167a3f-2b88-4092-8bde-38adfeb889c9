package su.reddot.domain.service.concierge.miniapps;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.CategoryDTO;
import su.reddot.domain.service.dto.concierge.ShopperBrandFilterDto;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoryFilterDto;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultShopperService implements ShopperService {
    
    private final BrandService brandService;
    private final CategoryService categoryService;
    
    @Override
    public ShopperBrandsResponseDto getBrands() {
        log.debug("Получение брендов для шопера");

        try {
            List<BrandDTO> brandDTOs = brandService.getBrandsAvailableForPublishing(null);

            List<ShopperBrandFilterDto> brands = brandDTOs.stream()
                    .map(this::mapBrandToFilterDto)
                    .collect(Collectors.toList());

            return new ShopperBrandsResponseDto()
                    .setBrands(brands)
                    .setTotalCount(brands.size());

        } catch (Exception e) {
            log.error("Ошибка при получении брендов для шопера", e);
            return new ShopperBrandsResponseDto()
                    .setBrands(Collections.emptyList())
                    .setTotalCount(0);
        }
    }

    @Override
    public ShopperCategoriesResponseDto getCategories() {
        log.debug("Получение категорий для шопера");

        try {
            List<CategoryDTO> categoryDTOs = categoryService.getDirectChildrenCategoryDTOsCached(CategoryService.ROOT_CATEGORY_ID, true);

            List<ShopperCategoryFilterDto> categories = categoryDTOs.stream()
                    .map(this::mapCategoryToFilterDto)
                    .collect(Collectors.toList());

            return new ShopperCategoriesResponseDto()
                    .setCategories(categories)
                    .setTotalCount(categories.size());

        } catch (Exception e) {
            log.error("Ошибка при получении категорий для шопера", e);
            return new ShopperCategoriesResponseDto()
                    .setCategories(Collections.emptyList())
                    .setTotalCount(0);
        }
    }
    
    /**
     * Маппинг BrandDTO в ShopperBrandFilterDto
     *
     * @param brandDTO исходный объект бренда
     * @return DTO для фильтра шопера, содержащий информацию о бренде
     */
    private ShopperBrandFilterDto mapBrandToFilterDto(BrandDTO brandDTO) {
        return new ShopperBrandFilterDto()
                .setId(brandDTO.getId())
                .setName(brandDTO.getName())
                .setUrlName(brandDTO.getUrlName())
                .setTransliterateName(brandDTO.getTransliterateName())
                .setProductsCount(brandDTO.getProductsCount())
                .setIsHidden(brandDTO.getIsHidden())
                .setTitle(brandDTO.getTitle())
                .setHiddenDescription(brandDTO.getHiddenDescription());
    }
    /**
     * Маппинг CategoryDTO в ShopperCategoryFilterDto
     *
     * @param categoryDTO исходный объект категории
     * @return DTO для фильтра шопера, содержащий информацию о категории
     */
    private ShopperCategoryFilterDto mapCategoryToFilterDto(CategoryDTO categoryDTO) {
        ShopperCategoryFilterDto dto = new ShopperCategoryFilterDto()
                .setId(categoryDTO.getId())
                .setDisplayName(categoryDTO.getDisplayName())
                .setSingularName(categoryDTO.getSingularName())
                .setFullName(categoryDTO.getFullName())
                .setPluralName(categoryDTO.getPluralName())
                .setUrl(categoryDTO.getUrl())
                .setAlternativeUrl(categoryDTO.getAlternativeUrl())
                .setIcon(categoryDTO.getIcon())
                .setProductsCount(categoryDTO.getProductsCount())
                .setHasChildren(categoryDTO.getHasChildren())
                .setDefaultSizeType(categoryDTO.getDefaultSizeType() != null ? categoryDTO.getDefaultSizeType().name() : null);
        
        // Маппинг дочерних категорий
        if (!CollectionUtils.isEmpty(categoryDTO.getChildren())) {
            List<ShopperCategoryFilterDto> children = categoryDTO.getChildren().stream()
                    .map(this::mapCategoryToFilterDto)
                    .collect(Collectors.toList());
            dto.setChildren(children);
        }
        
        return dto;
    }

}
