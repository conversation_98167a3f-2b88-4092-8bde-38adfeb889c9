package su.reddot.domain.dao.category;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.service.salesapp.model.LookupDto;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

public interface CategoryRepository extends JpaRepository<Category, Long>, QuerydslPredicateExecutor<Category> {

	long ROOT_CATEGORY_ID = 1L;

	/**
	 * Get all categories which are not logically removed
	 * @return List of categories
	 */
	@Query("select c from Category c where c.isDeleted = false and c.leftOrder >= 1 order by c.leftOrder")
    List<Category> findAllWithoutDeleted();

	/**
	 * Получить список всех категорий, за исключением корневой ( служебной ) категории
	 * @return список всех категорий в порядке от левой дочерней категории, к правой, рекурсивно.
	 * Например, дерево категорий:
	 *
	 *       Root
	 *    /   |   \
	 *   A    B    C
	 * / |    |    | \
	 * D E    G    H  I
	 *
	 * будет возвращено в виде списка категорий:
	 *
	 * ROOT A D E B G C H I
	 */
	@Query("select c from Category c where c.leftOrder >= 1 order by c.leftOrder")
	List<Category> findAll();

    /**
     * Получить список всех предков для данной категории, от дальних к близким,
     * <b>за исключением корневой (служебной) категории</b>.
     * @param categoryId идентификатор категории, для которой нужно получить ее предков.
     * @return список категорий - предков
     */
    @Query("select c from Category c " +
            "where c.leftOrder < (select c.leftOrder from Category c where c.id = ?1) " +
            "and c.rightOrder > (select c.rightOrder from Category c where c.id = ?1)" +
            "and c.leftOrder <> 1" +
			"and c.isDeleted = false " +
            "order by c.leftOrder"
    )
    List<Category> findAllParents(Long categoryId);

    @Query("select c.id from Category c " +
            "where c.leftOrder < (select c.leftOrder from Category c where c.id = ?1) " +
            "and c.rightOrder > (select c.rightOrder from Category c where c.id = ?1)" +
            "and c.leftOrder <> 1" +
			"and c.isDeleted = false " +
            "order by c.leftOrder"
    )
    List<Long> findAllParentIds(Long categoryId);

    /**
     * Получить список всех непосредственных категорий-детей для данной категории-родителя.
     * @param parentId идентификатор родительской категории
     * @return список дочерних категорий
     */
    @Query("select c from Category c where c.parent.id = ?1 order by c.leftOrder")
    List<Category> findChildrenCategories(Long parentId);

    /**
     * Получить список всех непосредственных категорий-детей для данной категории-родителя отсортированных по названию.
     * @param parentId идентификатор родительской категории
     * @return список дочерних категорий
     */
    @Query("select c from Category c where c.parent.id = ?1 and c.isDeleted = false order by c.displayName")
    List<Category> findChildrenCategoriesOrderByDisplayName(Long parentId);

    /**
     * Получить список дочерних категорий - "листьев", то есть тех категорий,
     * у которых нет собственных дочерних категорий.
     *
     * Например, если существует дерево категорий:
     *
     *       Root
     *    /   |   \
     *   A    B    C
     * / |    |    | \
     * D E    G    H  I
     *      /   \
     *     J     K
     *
     * то для категории B метод вернет категории J, K.
     * @param parentId идентификатор родительской категории
     * @return список категорий - "листьев" для данной родительской категории
     */
    default List<Category> findLeafCategories(Long parentId){
    	return findAllById(findLeafCategoryIds(parentId));
    }

	@Query("select c.id from Category c " +
			"where c.leftOrder >= (select c.leftOrder from Category c where c.id = ?1) " +
			"and c.rightOrder <= (select c.rightOrder from Category c where c.id = ?1)" +
			"and c.rightOrder = c.leftOrder + 1 " +
			"and c.isDeleted = false " +
			"order by c.leftOrder"
	)
	List<Long> findLeafCategoryIds(Long parentId);
	default Collection<Long> findAllLeafCategoryIds(Collection<Long> parentIds){
		Set<Long> result = new HashSet<>();
		for(Long parentId : parentIds){
			if(parentId == null) continue;
			result.addAll(findLeafCategoryIds(parentId));
		}
		return result;
	}

	/**
	 * Выбрать ID всех категорий дерева, включая переданную категорию, конечные и неконечные подкатегории всех уровней
	 * @param parentId
	 * @return
	 */
	@Query("select c.id from Category c " +
            "where c.leftOrder >= (select c.leftOrder from Category c where c.id = ?1) " +
            "and c.rightOrder <= (select c.rightOrder from Category c where c.id = ?1) " +
			"and c.isDeleted = false " +
            "order by c.leftOrder"
    )
    List<Long> findAllTreeCategoryIds(Long parentId);

    /**
     * Получить список всех категорий - листьев дерева.
     * @return список всех категорий - листьев.
     */
    @Query("select c from Category c where c.isDeleted = false and c.rightOrder = c.leftOrder + 1 order by c.leftOrder")
    List<Category> findAllLeafCategories();

    /**
     * Получить список корневых категорий.
     * @return список корневых категорий.
     */
    @Query("select c from Category c where c.parent = " +
            "(select c.id from Category c where c.leftOrder = 1)")
    List<Category> findRootCategories();

    /**
     * @return список идентификаторов категорий, в которых нет ни одного товара.
     */
    @Query(value =  "SELECT p_id FROM (SELECT p_id, coalesce(sum(amount), 0) amount" +
            "  FROM (" +
            "         SELECT" +
            "           p.id  p_id," +
            "           c.id  c_id" +
            "         FROM category p CROSS JOIN category c" +
            "         WHERE" +
            "           p.left_order <= c.left_order AND p.right_order >= c.right_order AND" +
            "           c.right_order - c.left_order = 1 AND" +
            "           p.left_order <> 1) cats" +
            "    LEFT JOIN (" +
            "                SELECT" +
            "                  category_id," +
            "                  count(*) amount" +
            "                FROM product INNER JOIN product_item ON product_state = 'PUBLISHED' and product.id = product_item.product_id AND delete_time IS NULL" +
            "                GROUP BY category_id) product_by_category" +
            "                ON cats.c_id = product_by_category.category_id" +
            "  GROUP BY p_id) t" +
            "  WHERE amount = 0",

            nativeQuery = true)
    List<BigInteger> findEmpty();

    /**
     * @return количество товаров по категориям: id -> count.
     */
    @Query(value =  "SELECT p_id, amount FROM (SELECT p_id, coalesce(sum(amount), 0) amount " +
		    "FROM ( " +
		        "SELECT p.id  p_id, c.id  c_id " +
		        "FROM category p CROSS JOIN category c " +
		        "WHERE p.left_order <= c.left_order AND p.right_order >= c.right_order AND c.right_order - c.left_order = 1 AND p.left_order <> 1 " +
				"and p.is_deleted = false and c.is_deleted = false" +
		    ") cats " +
		    "LEFT JOIN ( " +
		        "SELECT category_id, count(*) amount " +
		        "FROM product WHERE product_state = 'PUBLISHED' " +
		        "GROUP BY category_id) product_by_category " +
		        "ON cats.c_id = product_by_category.category_id " +
		    "GROUP BY p_id) t",
            nativeQuery = true)
    List<Object[]> countProductsRows();

	/**
	 * @return количество товаров по категориям: id -> count.
	 */
	default Map<Long, Integer> countProducts(){
		Map<Long, Integer> result = new HashMap<>();
		for(Object[] columns : countProductsRows()){
			Long categoryId = new Long(((BigInteger) columns[0]).longValue());
			Integer count = new Integer(((BigDecimal) columns[1]).intValue());
			result.put(categoryId, count);
		}
		return result;
	}



    /**
     * Изменить левый порядок всех категорий, которые будут расположены <b>правее</b>
     * в дереве относительно вставляемой категории.
     * При добавлении новой категории для сохранения достоверности данных
     * нужно также <b>обязательно</b> вызвать
     * {@link #shiftRightOrdersBeforeInsertingNewCategory}
     *
     *          Root Element
     *        /  |  \      \
     *       A  X   D*     G*
     *     / |  ^   \ \     \
     *    B C   ^   E* F*   H*
     *          ^
     *          +-- вставляемая категория
     *
     * (*) - категории, у которых изменятся левые порядки
     *
     * @param newCategoryLeftOrder левый порядок новой категории
     */
    @Modifying(clearAutomatically = true)
    @Query("update Category c set c.leftOrder = c.leftOrder + 2 where c.leftOrder >= ?1")
    void shiftLeftOrdersBeforeInsertingNewCategory(int newCategoryLeftOrder);

    /**
     * Изменить правый порядок всех категорий, которые расположены <b>правее</b>
     * в дереве относительно вставляемой категории, а также порядок всех ее предков.
     * При добавлении новой категории для сохранения достоверности данных
     * нужно также <b>обязательно</b> вызвать
     * {@link #shiftLeftOrdersBeforeInsertingNewCategory}
     *
     * @param newCategoryLeftOrder левый порядок новой категории
     */
    @Modifying(clearAutomatically = true)
    @Query("update Category c set c.rightOrder = c.rightOrder + 2 where c.rightOrder >= ?1")
    void shiftRightOrdersBeforeInsertingNewCategory(int newCategoryLeftOrder);

    @Query("select c from Category c " +
            "where c.leftOrder = " +
                "(select min(c.leftOrder) from Category c " +
                "where c.leftOrder < :#{#cat.leftOrder} " +
                "and c.rightOrder > :#{#cat.rightOrder} and c.leftOrder <> 1 " +
                "and (c.parent = :#{#parent} or :#{#parent} is null))")
    Optional<Category> findFirstNearest(@Param("cat") Category c, @Param("parent") Category nullableParent);

	@Query("FROM Category c WHERE c.id IN :ids")
	List<Category> findCategoryNamesByIds(Set<Long> ids);

	@Query("select c from Category c " +
		"where c.leftOrder = (" +
		"select max(c3.leftOrder) from Category c3 " +
		"where c3.leftOrder <= (select c2.leftOrder from Category c2 where c2.id = :categoryId) " +
		"and c3.rightOrder >= (select c2.rightOrder from Category c2 where c2.id = :categoryId) " +
		"and c3.alignment is not null and c3.alignment <> '' " +
		"and c3.indentBottom is not null) AND c.isDeleted != TRUE")
	Optional<Category> findTop1AlignmentAndIndent(@Param("categoryId") Long categoryId);
}
